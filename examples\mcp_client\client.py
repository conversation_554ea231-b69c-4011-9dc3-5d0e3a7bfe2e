#!/usr/bin/env python3

import asyncio
import json
import sys

from typing import Any, Dict, Optional

try:
    from fastmcp import Client
except ImportError:
    print("Error: fastmcp not installed. Please install it with 'pip install fastmcp'")
    sys.exit(1)


class MCPClient:

    def __init__(self, server_url: str="http://localhost:8000/mcp"):
        self.server_url = server_url
        self.client = Client(server_url)
        self.session_id = None

    async def connect(self) -> bool:
        try:
            await self.client.__aenter__()
            print(f"Connected to MCP Server at {self.server_url}")
            return True
        except Exception as e:
            print(f"Error connecting to MCP Server: {e}")
            return False

    async def disconnect(self):
        try:
            await self.client.__aexit__(None, None, None)
            print("Disconnected from MCP Server")
        except Exception as e:
            print(f"Error disconnecting from MCP Server: {e}")

    async def list_tools(self):
        try:
            tools = await self.client.list_tools()
            tool_names = [tool.name for tool in tools]
            return tool_names
        except Exception as e:
            print(f"Error listing tools: {e}")
            return []

    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Optional[Any]:
        try:
            print(
                f"\n🔧 Calling tool {tool_name} with arguments {json.dumps(self.format_response(arguments), indent=2)}"
            )
            result = await self.client.call_tool(tool_name, arguments)
            # print(f"✅ Tool result: {result.data}")
            return result.data
        except Exception as e:
            print(f"Error calling tool: {e}")
            return None

    def format_response(self, obj: Any) -> Any:
        try:
            # Pydantic model
            if hasattr(obj, "dict") and callable(getattr(obj, "dict")):
                return self.format_response(obj.dict())

            # Plain dict
            if isinstance(obj, dict):
                return {key: self.format_response(value) for key, value in obj.items()}

            # List or tuple
            if isinstance(obj, (list, tuple)):
                return [self.format_response(item) for item in obj]

            # Custom
            if hasattr(obj, "__dict__"):
                return {key: self.format_response(value) for key, value in vars(obj).items() if not key.startswith("_")}

            # Fallback primitive
            return obj

        except Exception as e:
            print(f"Error formatting response: {e}")
            return {}


async def default_mode():
    client = MCPClient()

    if not await client.connect():
        print("Failed to connect to MCP Server")
        return

    try:
        print("Connected to MCP Server")

        print("\nTesting tools")
        tools = await client.list_tools()

        print("\nAvailable tools:")
        for tool in tools:
            print(f" - {tool}")

        if "schedule_property_tour" in tools:
            schedule_tour = await client.call_tool(
                "schedule_property_tour",
                {
                    "property_id": 123,
                    "renter_id": 456,
                    "tour_date": "2025-09-01T10:00:00",
                    "first_name": "John",
                    "last_name": "Doe",
                },
            )

            print("\nSchedule tour result:")
            print(json.dumps(client.format_response(schedule_tour), indent=2))
    except Exception as e:
        print(f"Error: {e}")
    finally:
        await client.disconnect()


async def interactive_mode():
    print("🎮 Interactive MCP Client Mode")
    print("=" * 40)
    print("Commands:")
    print("  tools - List available tools")
    print("  resources - List available resources")
    print("  call <tool_name> <json_args> - Call a tool")
    print("  info - Show server info")
    print("  quit - Exit")
    print("=" * 40)

    client = MCPClient()

    if not await client.connect():
        print("Failed to connect to MCP Server")
        return

    while True:
        try:
            command = input("> ").strip()
            if command in ("quit", "exit"):
                break
            elif command == "tools":
                tools = await client.list_tools()
                print("Available tools:")
                for t in tools:
                    print(" -", t)
            elif command.startswith("call "):
                parts = command.split(" ", 2)

                if len(parts) >= 2:
                    tool_name = parts[1]
                    args = {}
                    if len(parts) == 3:
                        try:
                            args = json.loads(parts[2])
                        except json.JSONDecodeError:
                            print("❌ Invalid JSON arguments")
                            continue
                    result = await client.call_tool(tool_name, args)
                    print("Tool result:")
                    print(json.dumps(client.format_response(result), indent=2))
                else:
                    print("❌ Invalid call command. Usage: call <tool_name> <json_args>")
        except KeyboardInterrupt:
            break
        except EOFError:
            break


def main():
    if len(sys.argv) > 1 and sys.argv[1] == "--interactive":
        asyncio.run(interactive_mode())
    else:
        asyncio.run(default_mode())


if __name__ == "__main__":
    main()
