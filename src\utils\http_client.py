import os
from enum import Enum
from typing import Any
from urllib.parse import urljoin

import aiohttp
import requests
from starlette import status


class Service(Enum):
    """
    Enum representing the supported services for HTTP requests.
    Currently, only 'knock' is supported.
    """

    KNOCK = "knock"


# -- Private utility functions --
def _build_url_and_headers(service: str, endpoint: str) -> tuple[str, dict[str, str]]:
    """
    Build the full URL and headers for the given API endpoint and service.

    Args:
        service (str): The service to send the request to (e.g., "knock").
        endpoint (str): The API endpoint path (e.g., "/v1/internal/prospects/{prospect_id}/urls/schedule").

    Returns:
        tuple[str, dict[str, str]]: A tuple containing the full URL and the headers dictionary.

    Raises:
        RuntimeError: If the required environment variable (e.g., KNOCK_HOST) is not set.
        ValueError: If the service is not supported.
    """
    if service == Service.KNOCK:
        host = os.getenv("KNOCK_HOST")
        if not host:
            raise RuntimeError("KNOCK_HOST environment variable is not set. Cannot build Knock API URL.")
        token = os.getenv("KNOCK_INTERNAL_AUTH_TOKEN")
        headers = {"Content-Type": "application/json"}
        if token:
            headers["Internal-Authorization"] = f"Bearer {token}"

        url = urljoin(host, endpoint)
        return url, headers

    raise ValueError(f"Unsupported service '{service}' for building URL and headers.")


# -- Public utility functions --
def make_http_request(
    url: str,
    method: str,
    headers: dict[str, str] | None = None,
    json: dict[str, Any] | None = None,
    params: dict[str, str] | None = None,
) -> Any:
    """
    Perform a synchronous HTTP request.

    Args:
        url (str): The URL to request.
        method (str): The HTTP method to use (e.g., "GET", "POST", etc.).
        headers (dict[str, str] | None): Optional HTTP headers to include in the request.
        json (dict[str, Any] | None): Optional JSON data to send in the request body.
        params (dict[str, str] | None): Optional query parameters to include in the URL.

    Returns:
        Any: The parsed JSON response from the server, or `None` if the response was a 204 (No Content).

    Raises:
        requests.HTTPError: If the response status code indicates an error.
    """
    response = requests.request(
        method.upper(),
        url,
        headers=headers,
        json=json,
        params=params,
    )
    if not (200 <= response.status_code < 300):  # noqa: PLR2004
        raise requests.HTTPError(
            f"{method.upper()} {url} failed with {response.status_code}: {response.reason}; Response Body: {response.text}",  # noqa: E501
            response=response,
        )
    if response.status_code == status.HTTP_204_NO_CONTENT:
        return None
    return response.json()


async def make_async_http_request(
    url: str,
    method: str,
    headers: dict[str, str] | None = None,
    json: dict[str, Any] | None = None,
    params: dict[str, str] | None = None,
) -> dict | None:
    """
    Perform an asynchronous HTTP request.

    Args:
        url (str): The URL to request.
        method (str): The HTTP method to use (e.g., "GET", "POST", etc.).
        headers (dict[str, str] | None): Optional HTTP headers to include in the request.
        json (dict[str, Any] | None): Optional JSON data to send in the request body.
        params (dict[str, str] | None): Optional query parameters to include in the URL.

    Returns:
        Any: The parsed JSON response from the server, or `None` if the response was a 204 (No Content).

    Raises:
        aiohttp.ClientResponseError: If the response status code indicates an error.
    """
    async with aiohttp.ClientSession() as session:  # noqa: SIM117
        async with session.request(method.upper(), url, headers=headers, json=json, params=params) as response:
            if not (200 <= response.status < 300):  # noqa: PLR2004
                raise aiohttp.ClientResponseError(
                    request_info=response.request_info,
                    history=response.history,
                    status=response.status,
                    message=f"{method.upper()} {url} failed with {response.status}: {response.reason}; Response Body: {await response.text()}",  # noqa: E501
                    headers=response.headers,
                )
            # For responses with no content (e.g., 204), return None
            if response.status == status.HTTP_204_NO_CONTENT:
                return None
            return await response.json()


# -- High-level API functions --
def make_knock_api_request(
    endpoint: str,
    method: str = "GET",
    json: dict | None = None,
    params: dict[str, str] | None = None,
) -> Any:
    """
    Make a synchronous HTTP request to the Knock API.

    This function builds the full Knock API URL and appropriate headers using environment variables,
    then performs a synchronous HTTP request with the given method, JSON body, and query parameters.

    Args:
        endpoint (str): The API endpoint path (e.g., "/v1/internal/prospects/{prospect_id}/urls/schedule").
        method (str): The HTTP method to use (default is "GET").
        json (dict | None): Optional JSON data to send in the request body.
        params (dict | None): Optional query parameters to include in the URL.

    Returns:
        Any: The parsed JSON response from the server, or `None` if the response was a 204 (No Content).

    Raises:
        RuntimeError: If KNOCK_HOST is not set.
        requests.HTTPError: If the response status code indicates an error.
    """
    url, headers = _build_url_and_headers(Service.KNOCK, endpoint)
    return make_http_request(url, method, headers=headers, json=json, params=params)


async def make_async_knock_api_request(
    endpoint: str,
    method: str = "GET",
    json: dict | None = None,
    params: dict[str, str] | None = None,
) -> Any:
    """
    Make an asynchronous HTTP request to the Knock API.

    This function builds the full Knock API URL and appropriate headers using environment variables,
    then performs an asynchronous HTTP request with the given method, JSON body, and query parameters.

    Args:
        endpoint (str): The API endpoint path (e.g., "/v1/internal/prospects/{prospect_id}/urls/schedule").
        method (str): The HTTP method to use (default is "GET").
        json (dict | None): Optional JSON data to send in the request body.
        params (dict | None): Optional query parameters to include in the URL.

    Returns:
        Any: The parsed JSON response from the server, or `None` if the response was a 204 (No Content).

    Raises:
        RuntimeError: If KNOCK_HOST is not set.
        aiohttp.ClientResponseError: If the response status code indicates an error.
    """
    url, headers = _build_url_and_headers(Service.KNOCK, endpoint)
    return await make_async_http_request(url, method, headers=headers, json=json, params=params)
