import os
from unittest.mock import AsyncMock, MagicMock, patch

import aiohttp
import pytest
from starlette import status

from src.models.tour import ScheduleTourLink
from src.tools.tour import register_tour_tools, retrieve_schedule_tour_link
from tests.unit.tools.conftest import DummyMCP


class TestRetrieveScheduleTourLink:
    @pytest.mark.asyncio
    @patch("src.tools.tour.make_async_knock_api_request", new_callable=AsyncMock)
    async def test_retrieve_schedule_tour_link_success(self, mock_make_request):
        mock_make_request.return_value = {"status_code": "ok", "url": "http://alpha-shorturl.knocktest.com/WblkdQ"}
        prospect_id = 42

        result = await retrieve_schedule_tour_link(prospect_id)
        assert isinstance(result, ScheduleTourLink)
        assert str(result.url) == "http://alpha-shorturl.knocktest.com/WblkdQ"
        mock_make_request.assert_awaited_once_with(
            endpoint=f"/v1/internal/prospects/{prospect_id}/urls/schedule",
            method="GET",
        )

    @pytest.mark.asyncio
    @patch("src.tools.tour.make_async_knock_api_request", new_callable=AsyncMock)
    async def test_retrieve_schedule_tour_link_no_url_in_response(self, mock_make_request):
        mock_make_request.return_value = {}
        prospect_id = 99

        with pytest.raises(ValueError, match="No schedule tour link found in response"):
            await retrieve_schedule_tour_link(prospect_id)

    @pytest.mark.asyncio
    @patch("src.tools.tour.make_async_knock_api_request", new_callable=AsyncMock)
    async def test_retrieve_schedule_tour_link_raises_other_exception(self, mock_make_request):
        mock_make_request.side_effect = RuntimeError("API failure")
        prospect_id = 77

        with pytest.raises(RuntimeError, match="API failure"):
            await retrieve_schedule_tour_link(prospect_id)

    @pytest.mark.asyncio
    @patch("aiohttp.ClientSession")
    async def test_async_http_request_raises_client_response_error_on_non_2xx(self, mock_client_session):
        # Create the mock response object
        mock_response = AsyncMock()
        mock_response.status = status.HTTP_404_NOT_FOUND
        mock_response.reason = "Not Found"
        mock_response.headers = {}
        mock_response.text = AsyncMock(return_value="Not found")  # <-- Needed for error message

        # Create the context manager for session.request
        mock_request_ctx_mgr = MagicMock()
        mock_request_ctx_mgr.__aenter__ = AsyncMock(return_value=mock_response)
        mock_request_ctx_mgr.__aexit__ = AsyncMock(return_value=None)

        # Create the mock session object
        mock_session_instance = MagicMock()
        mock_session_instance.request = MagicMock(return_value=mock_request_ctx_mgr)

        # aiohttp.ClientSession() returns the mock session (as an async context manager)
        mock_client_session.return_value.__aenter__.return_value = mock_session_instance

        with (
            patch.dict(os.environ, {"KNOCK_HOST": "https://mock.knocktest.com"}),
            pytest.raises(aiohttp.ClientResponseError) as excinfo,
        ):
            await retrieve_schedule_tour_link(prospect_id=123)
        assert excinfo.value.status == status.HTTP_404_NOT_FOUND


class TestRegisterTourTools:
    def test_register_tour_tools_creates_tools(self):
        mcp = DummyMCP()
        register_tour_tools(mcp)
        assert "cancel_tour" in mcp.tools
        assert "schedule_tour" in mcp.tools
        assert "get_property_available_times" in mcp.tools
        assert "get_schedule_tour_link" in mcp.tools

    def test_get_schedule_tour_link_tool_exists(self):
        mcp = DummyMCP()
        register_tour_tools(mcp)
        assert callable(mcp.tools["get_schedule_tour_link"])

    def test_get_property_available_times_tool_exists(self):
        mcp = DummyMCP()
        register_tour_tools(mcp)
        assert callable(mcp.tools["get_property_available_times"])

    def test_schedule_tour_exists(self):
        mcp = DummyMCP()
        register_tour_tools(mcp)
        assert callable(mcp.tools["schedule_tour"])

    def test_cancel_tour_exists(self):
        mcp = DummyMCP()
        register_tour_tools(mcp)
        assert callable(mcp.tools["cancel_tour"])
