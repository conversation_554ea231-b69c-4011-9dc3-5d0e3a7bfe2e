import os
from unittest.mock import AsyncMock, MagicMock, patch

import aiohttp
import pytest
import requests
from starlette import status

from utils.http_client import (
    Service,
    _build_url_and_headers,
    make_async_http_request,
    make_async_knock_api_request,
    make_http_request,
    make_knock_api_request,
)


class TestMakeHttpRequest:
    @patch("requests.request")
    def test_sync_http_request_success(self, mock_request):
        mock_response = MagicMock()
        mock_response.status_code = status.HTTP_200_OK
        mock_response.json.return_value = {"foo": "bar"}
        mock_response.reason = "OK"
        mock_request.return_value = mock_response

        url = "https://api.knock.com/v1/test"
        method = "GET"
        headers = {"Content-Type": "application/json"}
        json_data = {"key": "value"}
        params = {"q": "1"}

        result = make_http_request(url, method, headers=headers, json=json_data, params=params)

        mock_request.assert_called_once_with(
            method.upper(),
            url,
            headers=headers,
            json=json_data,
            params=params,
        )
        assert result == {"foo": "bar"}

    @patch("requests.request")
    def test_sync_http_request_raises_http_error_on_non_2xx(self, mock_request):
        mock_response = MagicMock()
        mock_response.status_code = status.HTTP_404_NOT_FOUND
        mock_response.reason = "Not Found"
        mock_response.text = "Not found"
        mock_request.return_value = mock_response

        url = "https://api.knock.com/v1/notfound"
        method = "GET"

        with pytest.raises(requests.HTTPError) as excinfo:
            make_http_request(url, method)

        assert "404" in str(excinfo.value)
        assert "Not Found" in str(excinfo.value)
        assert "Response Body: Not found" in str(excinfo.value)
        mock_request.assert_called_once()

    @patch("requests.request")
    def test_sync_http_request_returns_none_on_204(self, mock_request):
        mock_response = MagicMock()
        mock_response.status_code = status.HTTP_204_NO_CONTENT
        mock_response.reason = "No Content"
        mock_request.return_value = mock_response

        url = "https://api.knock.com/v1/delete"
        method = "DELETE"

        result = make_http_request(url, method)
        assert result is None
        mock_request.assert_called_once()


class TestMakeAsyncHttpRequest:
    @pytest.mark.asyncio
    @patch("aiohttp.ClientSession")
    async def test_async_http_request_success(self, mock_client_session):
        # Create the mock response object
        mock_response = AsyncMock()
        mock_response.status = status.HTTP_200_OK
        mock_response.json = AsyncMock(return_value={"foo": "bar"})
        mock_response.reason = "OK"
        mock_response.headers = {}

        # Create the context manager for session.request
        mock_request_ctx_mgr = MagicMock()
        mock_request_ctx_mgr.__aenter__ = AsyncMock(return_value=mock_response)
        mock_request_ctx_mgr.__aexit__ = AsyncMock(return_value=None)

        # Create the mock session object
        mock_session_instance = MagicMock()
        mock_session_instance.request = MagicMock(return_value=mock_request_ctx_mgr)

        # aiohttp.ClientSession() returns the mock session (as an async context manager)
        mock_client_session.return_value.__aenter__.return_value = mock_session_instance

        url = "https://api.knock.com/v1/test"
        method = "GET"
        headers = {"Content-Type": "application/json"}
        json_data = {"key": "value"}
        params = {"q": "1"}

        result = await make_async_http_request(url, method, headers=headers, json=json_data, params=params)

        mock_session_instance.request.assert_called_once_with(
            method.upper(),
            url,
            headers=headers,
            json=json_data,
            params=params,
        )
        assert result == {"foo": "bar"}

    @pytest.mark.asyncio
    @patch("aiohttp.ClientSession")
    async def test_async_http_request_raises_client_response_error_on_non_2xx(self, mock_client_session):
        # Create the mock response object
        mock_response = AsyncMock()
        mock_response.status = status.HTTP_404_NOT_FOUND
        mock_response.reason = "Not Found"
        mock_response.headers = {}
        mock_response.text = AsyncMock(return_value="Not found")  # <-- Needed for error message

        # Create the context manager for session.request
        mock_request_ctx_mgr = MagicMock()
        mock_request_ctx_mgr.__aenter__ = AsyncMock(return_value=mock_response)
        mock_request_ctx_mgr.__aexit__ = AsyncMock(return_value=None)

        # Create the mock session object
        mock_session_instance = MagicMock()
        mock_session_instance.request = MagicMock(return_value=mock_request_ctx_mgr)

        # aiohttp.ClientSession() returns the mock session (as an async context manager)
        mock_client_session.return_value.__aenter__.return_value = mock_session_instance

        url = "https://api.knock.com/v1/notfound"
        method = "GET"

        with pytest.raises(aiohttp.ClientResponseError) as excinfo:
            await make_async_http_request(url, method)

        assert excinfo.value.status == status.HTTP_404_NOT_FOUND
        assert "Not Found" in str(excinfo.value)
        assert "Response Body: Not found" in str(excinfo.value)
        mock_session_instance.request.assert_called_once()

    @pytest.mark.asyncio
    @patch("aiohttp.ClientSession")
    async def test_async_http_request_returns_none_on_204(self, mock_client_session):
        # Create the mock response object
        mock_response = AsyncMock()
        mock_response.status = status.HTTP_204_NO_CONTENT
        mock_response.reason = "No Content"
        mock_response.headers = {}

        # Create the context manager for session.request
        mock_request_ctx_mgr = MagicMock()
        mock_request_ctx_mgr.__aenter__ = AsyncMock(return_value=mock_response)
        mock_request_ctx_mgr.__aexit__ = AsyncMock(return_value=None)

        # Create the mock session object
        mock_session_instance = MagicMock()
        mock_session_instance.request = MagicMock(return_value=mock_request_ctx_mgr)

        # aiohttp.ClientSession() returns the mock session (as an async context manager)
        mock_client_session.return_value.__aenter__.return_value = mock_session_instance

        url = "https://api.knock.com/v1/delete"
        method = "DELETE"

        result = await make_async_http_request(url, method)
        assert result is None
        mock_session_instance.request.assert_called_once()


class TestMakeKnockApiRequest:
    @patch("utils.http_client.make_http_request")
    def test_make_knock_api_request_basic(self, mock_make_http_request):
        mock_make_http_request.return_value = {"result": "ok"}
        endpoint = "/v1/internal/test"
        method = "POST"
        json_data = {"foo": "bar"}
        params = {"verbose": "true"}

        with patch.dict("os.environ", {"KNOCK_HOST": "https://api.knock.com", "KNOCK_INTERNAL_AUTH_TOKEN": "token123"}):
            result = make_knock_api_request(endpoint, method, json_data, params)

        expected_url = "https://api.knock.com/v1/internal/test"
        expected_headers = {"Content-Type": "application/json", "Internal-Authorization": "Bearer token123"}
        mock_make_http_request.assert_called_once_with(
            expected_url,
            method,
            headers=expected_headers,
            json=json_data,
            params=params,
        )
        assert result == {"result": "ok"}

    def test_make_knock_api_request_missing_host(self, monkeypatch):
        monkeypatch.delenv("KNOCK_HOST", raising=False)
        with pytest.raises(RuntimeError, match="KNOCK_HOST environment variable is not set"):
            make_knock_api_request(endpoint="/v1/test")

    @patch("utils.http_client.make_http_request")
    def test_make_knock_api_request_no_auth_token(self, mock_make_http_request):
        mock_make_http_request.return_value = {"status": "no_auth"}
        endpoint = "/v1/internal/noauth"
        method = "GET"

        with patch.dict("os.environ", {"KNOCK_HOST": "https://api.knock.com"}, clear=True):
            result = make_knock_api_request(endpoint, method)

        expected_url = "https://api.knock.com/v1/internal/noauth"
        expected_headers = {"Content-Type": "application/json"}
        mock_make_http_request.assert_called_once_with(
            expected_url,
            method,
            headers=expected_headers,
            json=None,
            params=None,
        )
        assert result == {"status": "no_auth"}


class TestMakeAsyncKnockApiRequest:
    @pytest.mark.asyncio
    @patch("utils.http_client.make_async_http_request", new_callable=AsyncMock)
    async def test_make_async_knock_api_request_basic(self, mock_make_async_http_request):
        # Setup
        mock_make_async_http_request.return_value = {"result": "ok"}
        endpoint = "/v1/internal/test"
        method = "GET"
        json_data = {"foo": "bar"}
        params = {"verbose": "true"}

        # Patch environment variables
        with patch.dict(os.environ, {"KNOCK_HOST": "https://api.knock.com", "KNOCK_INTERNAL_AUTH_TOKEN": "token123"}):
            result = await make_async_knock_api_request(endpoint, method, json_data, params)

        # Check URL and headers passed to make_async_http_request
        expected_url = "https://api.knock.com/v1/internal/test"
        expected_headers = {"Content-Type": "application/json", "Internal-Authorization": "Bearer token123"}
        mock_make_async_http_request.assert_awaited_once_with(
            expected_url,
            method,
            headers=expected_headers,
            json=json_data,
            params=params,
        )
        assert result == {"result": "ok"}

    @pytest.mark.asyncio
    async def test_make_async_knock_api_request_missing_host(self, monkeypatch):
        monkeypatch.delenv("KNOCK_HOST", raising=False)
        with pytest.raises(RuntimeError, match="KNOCK_HOST environment variable is not set"):
            await make_async_knock_api_request(endpoint="/v1/test")

    @pytest.mark.asyncio
    @patch("utils.http_client.make_async_http_request", new_callable=AsyncMock)
    async def test_make_async_knock_api_request_no_auth_token(self, mock_make_async_http_request):
        mock_make_async_http_request.return_value = {"status": "no_auth"}
        endpoint = "/v1/internal/noauth"
        method = "POST"

        with patch.dict(os.environ, {"KNOCK_HOST": "https://api.knock.com"}, clear=True):
            result = await make_async_knock_api_request(endpoint, method)

        expected_url = "https://api.knock.com/v1/internal/noauth"
        expected_headers = {"Content-Type": "application/json"}
        mock_make_async_http_request.assert_awaited_once_with(
            expected_url,
            method,
            headers=expected_headers,
            json=None,
            params=None,
        )
        assert result == {"status": "no_auth"}


class TestBuildUrlAndHeaders:
    def test_knock_service_with_host_and_internal_endpoint(self):
        endpoint = "/v1/internal/foo"
        with patch.dict("os.environ", {"KNOCK_HOST": "https://api.knock.com", "KNOCK_INTERNAL_AUTH_TOKEN": "tok"}):
            url, headers = _build_url_and_headers(Service.KNOCK, endpoint)
        assert url == "https://api.knock.com/v1/internal/foo"
        assert headers == {"Content-Type": "application/json", "Internal-Authorization": "Bearer tok"}

    def test_knock_service_missing_host_raises(self):
        endpoint = "/v1/foo"
        with (
            patch.dict("os.environ", {}, clear=True),
            pytest.raises(RuntimeError, match="KNOCK_HOST environment variable is not set"),
        ):
            _build_url_and_headers(Service.KNOCK, endpoint)

    def test_knock_service_with_host_but_no_token(self):
        endpoint = "/v1/internal/bar"
        with patch.dict("os.environ", {"KNOCK_HOST": "https://api.knock.com"}, clear=True):
            url, headers = _build_url_and_headers(Service.KNOCK, endpoint)
        assert url == "https://api.knock.com/v1/internal/bar"
        assert headers == {"Content-Type": "application/json"}

    def test_unsupported_service_raises(self):
        endpoint = "/v1/foo"
        with (
            patch.dict("os.environ", {"KNOCK_HOST": "https://api.knock.com"}),
            pytest.raises(ValueError, match="Unsupported service"),
        ):
            _build_url_and_headers("not_supported", endpoint)
