from datetime import UTC, date, datetime, timedelta

from pytz import timezone as ptz

from src.models.appointment import Appointment, PropertyAvailableTimeslots
from src.utils.log_config import get_logger

logger = get_logger(__name__)


def get_natural_date_label(
    target_date: date | datetime | str, tz_str: str | None = None, fmt: str | None = "%A, %B %d"
) -> str:
    """
    Returns 'today', 'tomorrow', or a formatted date string based on the target date and timezone.
    - target_date: a date or datetime object
    - tz_str: timezone (e.g., 'America/Chicago'). If None, uses local time.
    - fmt: format for fallback date string
    """

    if isinstance(target_date, str):
        target_date = datetime.strptime(target_date, "%Y-%m-%d").date()
    elif isinstance(target_date, datetime):
        target_date = target_date.date()

    if tz_str:
        try:
            tz = ptz(tz_str)
            today = datetime.now(tz).date()
        except Exception:
            today = date.today()
    else:
        today = date.today()

    if target_date == today:
        return "today"
    if target_date == today + timedelta(days=1):
        return "tomorrow"
    # This will return e.g. 'Friday, May 30'
    return target_date.strftime(fmt)


def parse_datetime_with_optional_tz(dt_str: str) -> datetime:
    """
    Parses a datetime string with optional timezone information.
    If the string does not contain timezone info, it assumes UTC.
    """
    try:
        # Try parsing with timezone info
        return datetime.strptime(dt_str, "%Y-%m-%dT%H:%M:%S%z")
    except ValueError:
        # Fallback: parse without timezone, assume UTC
        dt = datetime.strptime(dt_str, "%Y-%m-%dT%H:%M:%S")
        return dt.replace(tzinfo=UTC)


def compress_timeslots(data, date_from=None, date_to=None, property_timezone=None):
    grouped = {}

    for dt_str in data:
        try:
            dt = parse_datetime_with_optional_tz(dt_str)
            date_part = dt.date()
            time_part = dt.time().strftime("%H:%M:%S")
            offset_part = dt.strftime("%z")
            offset_part = offset_part[:3] + ":" + offset_part[3:]  # Format '-0600' as '-06:00'
        except ValueError:
            continue

        if date_from and date_part < date_from:
            continue
        if date_to and date_part > date_to:
            continue

        date_part_full = date_part.strftime("%A, %B %d, %Y")
        natural_label = get_natural_date_label(date_part, property_timezone)

        if natural_label in ["today", "tomorrow"]:
            date_part_full = f"{natural_label} - {date_part_full}"

        # Group by date
        if date_part_full not in grouped:
            grouped[date_part_full] = {"offset": offset_part, "times": []}
        grouped[date_part_full]["times"].append(time_part)

    # Sort times for each date
    for date_info in grouped.values():
        date_info["times"].sort()
        date_info["times"] = ", ".join(date_info["times"])

    return grouped


def get_available_times(property_id: int):
    # TODO: API Request to get available appointments
    # GET https://alpha-api.knocktest.com/v1/properties/{property_id}/available-times
    # Handle the API request and response parsing here
    logger.info(f"Fetching available appointments for property ID: {property_id}")
    return {
        "available_times": {
            "acceptable_times": [
                "2025-07-20T19:30:00-06:00",
                "2025-07-20T20:00:00-06:00",
                "2025-07-20T20:30:00-06:00",
                "2025-08-20T20:30:00-06:00",
                "2025-09-20T20:30:00-06:00",
            ],
            "reviewable_times": [],
        }
    }


def _get_property_available_timeslots(
    property_id: int,
    renter_id: int,
    date_from: date,
    date_to: date,
    property_timezone: str | None = None,
):
    if property_id is None:
        raise ValueError("Property ID cannot be None")
    if renter_id is None:
        raise ValueError("Renter ID cannot be None")

    # TODO: Create a utilifity function for getting auth token by renter ID
    auth_token = "AUTH_TOKEN_PLACEHOLDER"  # Replace with actual token retrieval logic
    if not auth_token:
        raise ValueError("Failed to retrieve authentication token")

    response = get_available_times(property_id)

    if len(response.get("available_times", {}).get("acceptable_times", [])) > 0:
        available_times = response.get("available_times", {}).get("acceptable_times", [])
        self_schedule_note = "self_schedule is ON, therefore appointments will be confirmed automatically"
        available_times = compress_timeslots(available_times, date_from, date_to, property_timezone)

    elif len(response.get("available_times", {}).get("reviewable_times", [])) > 0:
        reviewable_times = response.get("available_times", {}).get("reviewable_times", [])
        self_schedule_note = "self_schedule is OFF, therefore appointments will need to be reviewed before confirming"
        available_times = compress_timeslots(reviewable_times, date_from, date_to, property_timezone)

    else:
        logger.info(f"No available times found for property ID: {property_id}")
        return PropertyAvailableTimeslots(
            property_available_times={},
            searched_date_from=date_from,
            searched_date_to=date_to,
            self_schedule_note="No available times found",
        )

    return PropertyAvailableTimeslots(
        property_available_times=available_times,
        searched_date_from=date_from,
        searched_date_to=date_to,
        self_schedule_note=self_schedule_note,
    )


def _get_prospect_appointments(property_id: int, renter_id: int) -> Appointment:
    # TODO: Implement the logic to retrieve prospect appointments
    #       If no appointments are found, return "NO_SCHEDULED_APPOINTMENTS". Can.we raise 404 here?
    # get_appointments:
    #   api_version: "1"
    #   base_url: https://alpha-api.knocktest.com
    #   keys_to_keep:
    #     - id
    #     - status
    #     - start_time
    #   name: get_appointments
    #   operation_id: get_appointments
    #   path: /v1/appointments
    #   response_type: json
    #   replace_keys:
    #     id: appointment_id
    #     status: appointment_status
    #     start_time: appointment_start_time
    #   source: knock
    #   method: GET
    logger.info(f"Fetching prospect appointments for property ID: {property_id} and renter ID: {renter_id}")
    return Appointment(
        appointment_id=333,
        appointment_status="confirmed",
        appointment_start_time="2025-07-20T19:30:00-06:00",
    )


def register_appointment_tools(mcp):
    @mcp.tool(
        name="get_property_available_timeslots",
        description="Get available appointment timeslots for a property within a date range.",
    )
    def get_property_available_timeslots(
        property_id: int,
        renter_id: int,
        date_from: date,
        date_to: date,
        property_timezone: str | None = None,
    ) -> PropertyAvailableTimeslots:
        return _get_property_available_timeslots(property_id, renter_id, date_from, date_to, property_timezone)

    @mcp.tool(
        name="get_prospect_appointments",
        description="Get prospect appointments for a property and renter.",
    )
    def get_prospect_appointments(
        property_id: int,
        renter_id: int,
    ) -> Appointment:
        return _get_prospect_appointments(property_id, renter_id)
