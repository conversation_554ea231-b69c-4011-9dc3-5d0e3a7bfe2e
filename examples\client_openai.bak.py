"""
OpenAI MCP Client with HTTP Transport

This client connects to MCP servers over HTTP transport and provides an interactive
chat interface where OpenAI's GPT models can access and use tools from the connected
MCP server. It mirrors the functionality of the Claude client but uses OpenAI's API.

Setup Instructions:
1. Install dependencies:
   pip install mcp openai python-dotenv
   OR
   bun install mcp openai python-dotenv (if using bun)

2. Set OpenAI API key:
   export OPENAI_API_KEY=your_key_here
   OR create a .env file with:
   OPENAI_API_KEY=your_key_here

3. Start the MCP server with HTTP transport:
   uv run uvicorn main:app --host 0.0.0.0 --port 8000

4. Run this client:
   python client_openai.py [server_url]

Features:
- HTTP transport with comprehensive error handling and timeout management
- URL validation and connection retry logic
- Interactive chat interface with OpenAI GPT-4o model
- Automatic tool discovery and usage with function calling
- Structured and unstructured tool result handling
- Persistent conversation history with context management
- Progress reporting and detailed logging support
- Async/await patterns for optimal performance
- Graceful error handling and recovery

API Compatibility:
- Uses OpenAI's latest chat completions API with function calling
- Supports both GPT-4o and GPT-3.5-turbo models
- Compatible with OpenAI's tool/function calling format
- Handles streaming and non-streaming responses

Example Usage:
    # Connect to local MCP server (default)
    python client_openai.py

    # Connect to custom local server
    python client_openai.py http://localhost:8000/mcp

    # Connect to remote server
    python client_openai.py https://your-server.com/mcp

    # Interactive commands:
    > Hello, what tools are available?
    > clear  # Clear conversation history
    > quit   # Exit the client

Architecture:
This client follows the same architectural patterns as the Claude client:
- MCPClient class encapsulates all functionality
- Streamable HTTP transport for MCP communication
- Async context managers for resource cleanup
- Conversation history management
- Tool discovery and execution pipeline
"""

from __future__ import annotations
import asyncio
import json
import sys
from typing import Optional, List, Any, Dict
from contextlib import AsyncExitStack
from urllib.parse import urlparse
import copy
from pathlib import Path

from mcp import ClientSession
from mcp.client.streamable_http import streamablehttp_client

from openai import AsyncOpenAI
from openai.types.chat import ChatCompletion
from mcp.types import TextContent

from dotenv import load_dotenv

# Import flow configuration system
try:
    # Try to import from flows directory
    flows_dir = Path(__file__).parent / "flows"
    if flows_dir.exists():
        sys.path.insert(0, str(flows_dir.parent))
        from flows.flow_loader import flow_loader
        from flows.input_transformations import InputTransformationError
        FLOW_SYSTEM_AVAILABLE = True
    else:
        FLOW_SYSTEM_AVAILABLE = False
        print("⚠️  Flow configuration system not available - flows directory not found")
except ImportError as e:
    FLOW_SYSTEM_AVAILABLE = False
    print(f"⚠️  Flow configuration system not available: {e}")

load_dotenv()  # Load environment variables from .env file


def resolve_json_schema_refs(schema: Dict[str, Any]) -> Dict[str, Any]:
    """
    Resolve JSON Schema $ref references to create a flattened schema for OpenAI.

    OpenAI's function calling doesn't handle $ref as well as Claude, so we need
    to expand all references inline.

    Args:
        schema: The JSON schema with potential $ref references

    Returns:
        A new schema with all $ref references resolved inline
    """
    if not isinstance(schema, dict):
        return schema

    # Make a deep copy to avoid modifying the original
    resolved_schema = copy.deepcopy(schema)

    # Get the definitions section
    definitions = resolved_schema.get("$defs", {})

    def resolve_ref(obj: Any, path: Optional[List[str]] = None) -> Any:
        """Recursively resolve $ref references"""
        if path is None:
            path = []

        # Prevent infinite recursion
        if len(path) > 10:
            return obj

        if isinstance(obj, dict):
            if "$ref" in obj:
                ref_path = obj["$ref"]
                if ref_path.startswith("#/$defs/"):
                    def_name = ref_path.replace("#/$defs/", "")
                    if def_name in definitions and def_name not in path:
                        # Resolve the reference
                        resolved_def = resolve_ref(definitions[def_name], path + [def_name])
                        return resolved_def
                    else:
                        # Return a simplified version if we can't resolve
                        return {"type": "object"}
                return obj
            else:
                # Recursively resolve all values in the dict
                resolved_obj = {}
                for key, value in obj.items():
                    resolved_obj[key] = resolve_ref(value, path)
                return resolved_obj
        elif isinstance(obj, list):
            # Recursively resolve all items in the list
            return [resolve_ref(item, path) for item in obj]
        else:
            return obj

    # Resolve all references in the schema
    resolved_schema = resolve_ref(resolved_schema)

    # Remove the $defs section since we've inlined everything
    if "$defs" in resolved_schema:
        del resolved_schema["$defs"]

    return resolved_schema


class MCPClient:
    def __init__(self):
        self.session: Optional[ClientSession] = None
        self.exit_stack = AsyncExitStack()
        self.openai = AsyncOpenAI()
        self.conversation_history: List[Dict[str, Any]] = []  # Persistent conversation context
        self.available_tools: List[str] = []  # Track available tool names for flow configuration
        self.flow_enhanced_mode = FLOW_SYSTEM_AVAILABLE  # Enable flow enhancements if available

    async def connect(self, server_url: str = "http://localhost:8000/mcp"):
        """Connect to MCP Server over HTTP transport

        Args:
            server_url: HTTP URL of the MCP server (e.g., 'http://localhost:8000/mcp')
        """
        # Validate URL format
        try:
            parsed = urlparse(server_url)
            if not parsed.scheme or not parsed.netloc:
                raise ValueError("Invalid URL format")
            if parsed.scheme not in ("http", "https"):
                raise ValueError("URL must use http or https scheme")
        except Exception as e:
            raise ValueError(f"Invalid server URL '{server_url}': {e}")

        try:
            # Connect using streamable HTTP transport with timeout
            print("Establishing HTTP transport connection...")
            http_transport = await asyncio.wait_for(
                self.exit_stack.enter_async_context(streamablehttp_client(server_url)),
                timeout=30.0,  # 30 second timeout for connection
            )
            self.read_stream, self.write_stream, self.session_id = http_transport

            # Create MCP session
            print("Creating MCP session...")
            self.session = await self.exit_stack.enter_async_context(ClientSession(self.read_stream, self.write_stream))

            # Initialize the session with timeout
            print("Initializing session...")
            await asyncio.wait_for(
                self.session.initialize(),
                timeout=10.0,  # 10 second timeout for initialization
            )

            # List available tools
            print("Discovering available tools...")
            toolsData = await self.session.list_tools()
            tools = toolsData.tools
            print(f"\n✅ Connected to MCP server at {server_url}")
            print(f"📋 Available tools: {[tool.name for tool in tools]}")

            if self.session_id:
                print(f"🔗 Session ID: {self.session_id}")

        except asyncio.TimeoutError:
            raise RuntimeError(f"Connection timeout: MCP server at {server_url} did not respond within 30 seconds")
        except ConnectionError as e:
            raise RuntimeError(f"Connection failed: Unable to reach MCP server at {server_url}: {e}")
        except Exception as e:
            raise RuntimeError(f"Failed to connect to MCP server at {server_url}: {e}")

    async def disconnect(self):
        """
        Disconnect from MCP server and clean up resources.

        Properly closes the HTTP transport connection and cleans up
        any associated resources.
        """
        try:
            if self.exit_stack:
                print("Disconnecting from MCP server...")
                await self.exit_stack.aclose()
                print("✅ Disconnected successfully")
        except Exception as e:
            print(f"⚠️  Warning during disconnect: {e}")

    def clear_conversation_history(self):
        """Clear the conversation history to start fresh."""
        self.conversation_history.clear()
        print("🔄 Conversation history cleared")

    def _apply_input_transformations(self, tool_name: str, tool_args: Dict[str, Any]) -> Dict[str, Any]:
        """
        Apply automatic input transformations based on flow configuration.

        Args:
            tool_name: Name of the MCP tool being called
            tool_args: Original tool arguments from AI

        Returns:
            Transformed tool arguments with proper formatting
        """
        if not FLOW_SYSTEM_AVAILABLE:
            return tool_args

        try:
            # Load flow configuration for this tool
            flow_config = flow_loader.load_flow_configuration(tool_name)
            if not flow_config:
                return tool_args

            # Create a copy to avoid modifying the original
            transformed_args = tool_args.copy()

            # Apply transformations based on flow configuration
            input_transformations = flow_config.input_transformations
            transformer = flow_loader.input_transformer

            # Generic transformation logic that works for any tool
            transformed_args = self._transform_nested_data(transformed_args, input_transformations, transformer)

            return transformed_args

        except Exception as e:
            print(f"⚠️  Input transformation error for {tool_name}: {e}")
            return tool_args

    def _transform_nested_data(self, data: Any, input_transformations: Dict[str, Any], transformer) -> Any:
        """
        Recursively transform nested data structures based on input transformation configuration.

        Args:
            data: The data to transform (can be dict, list, or primitive)
            input_transformations: Available transformation types from flow config
            transformer: The input transformer instance

        Returns:
            Transformed data with the same structure
        """
        if isinstance(data, dict):
            transformed_dict = {}
            for key, value in data.items():
                # Check if this field should be transformed based on key name patterns
                transformation_type = self._get_transformation_type_for_field(key, input_transformations)

                if transformation_type and isinstance(value, str) and value:
                    # Check if already formatted to avoid double transformation
                    if not self._is_already_formatted(value, transformation_type):
                        try:
                            transformed_value = transformer.transform_input(transformation_type, value)
                            # Apply any post-processing (like timezone defaults)
                            transformed_value = self._apply_post_processing(transformed_value, transformation_type)
                            transformed_dict[key] = transformed_value
                            print(f"🔧 Auto-transformed {transformation_type}: '{value}' → '{transformed_value}'")
                        except Exception as e:
                            print(f"⚠️  {transformation_type} transformation failed for '{value}': {e}")
                            transformed_dict[key] = value
                    else:
                        transformed_dict[key] = value
                else:
                    # Recursively transform nested structures
                    transformed_dict[key] = self._transform_nested_data(value, input_transformations, transformer)
            return transformed_dict
        elif isinstance(data, list):
            return [self._transform_nested_data(item, input_transformations, transformer) for item in data]
        else:
            return data

    def _get_transformation_type_for_field(self, field_name: str, input_transformations: Dict[str, Any]) -> Optional[str]:
        """
        Determine the transformation type for a field based on its name and available transformations.

        Args:
            field_name: Name of the field
            input_transformations: Available transformation types

        Returns:
            Transformation type or None if no transformation should be applied
        """
        field_name_lower = field_name.lower()

        # Map common field name patterns to transformation types
        field_patterns = {
            'phone_number': ['phone', 'phone_number', 'phonenumber', 'mobile', 'cell'],
            'datetime': ['start_time', 'end_time', 'datetime', 'time', 'appointment_time', 'scheduled_time'],
            'move_date': ['move_date', 'target_move_date', 'movedate', 'move_in_date', 'movein_date']
        }

        for transformation_type, patterns in field_patterns.items():
            if transformation_type in input_transformations:
                for pattern in patterns:
                    if pattern in field_name_lower:
                        return transformation_type

        return None

    def _apply_post_processing(self, value: str, transformation_type: str) -> str:
        """
        Apply any post-processing to transformed values.

        Args:
            value: The transformed value
            transformation_type: Type of transformation applied

        Returns:
            Post-processed value
        """
        if transformation_type == "datetime":
            # Ensure timezone format for datetime
            if not value.endswith(('-05:00', '-06:00', '+00:00', '-04:00', '-07:00', '-08:00')):
                value += "-05:00"  # Default to Central Time

        return value

    def _is_already_formatted(self, value: str, transformation_type: str) -> bool:
        """
        Check if a value is already in the correct format for its transformation type.

        Args:
            value: The value to check
            transformation_type: Type of transformation

        Returns:
            True if already formatted, False otherwise
        """
        if transformation_type == "phone_number":
            return self._is_already_formatted_phone(value)
        elif transformation_type == "datetime":
            return self._is_already_formatted_datetime(value)
        elif transformation_type == "move_date":
            return self._is_already_formatted_date(value)

        return False

    def _is_already_formatted_phone(self, phone: str) -> bool:
        """Check if phone number is already in (XXX) XXX-XXXX format."""
        import re
        pattern = r'^\(\d{3}\) \d{3}-\d{4}$'
        return bool(re.match(pattern, phone))

    def _is_already_formatted_datetime(self, dt_str: str) -> bool:
        """Check if datetime is already in ISO format with timezone."""
        import re
        pattern = r'^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}[+-]\d{2}:?\d{2}$'
        return bool(re.match(pattern, dt_str))

    def _is_already_formatted_date(self, date_str: str) -> bool:
        """Check if date is already in YYYY-MM-DD format."""
        import re
        pattern = r'^\d{4}-\d{2}-\d{2}$'
        return bool(re.match(pattern, date_str))

    async def query(self, query: str) -> str:
        """Process query using OpenAI and available tools"""
        # Add new user message to conversation history
        self.conversation_history.append({"role": "user", "content": query})
        messages: List[Dict[str, Any]] = self.conversation_history.copy()

        if not self.session:
            raise RuntimeError("Not connected to MCP server. Call connect() first.")

        try:
            print("📋 Discovering available tools...")
            toolData = await self.session.list_tools()
            available_tools: List[Dict[str, Any]] = []
            self.available_tools = []  # Reset tool list

            for tool in toolData.tools:
                self.available_tools.append(tool.name)

                # Resolve JSON Schema references for OpenAI compatibility
                resolved_schema = resolve_json_schema_refs(tool.inputSchema)

                # Enhance tool description with flow configuration if available
                enhanced_description = tool.description or ""
                if self.flow_enhanced_mode and FLOW_SYSTEM_AVAILABLE:
                    flow_config = flow_loader.load_flow_configuration(tool.name)
                    if flow_config:
                        enhanced_description += f"\n\nINTELLIGENT INPUT HANDLING: This tool supports natural language input. " \
                                             f"You can accept user input in natural formats and transform it automatically. " \
                                             f"Follow the flow configuration guidance for optimal user experience."
                        print(f"🎯 Flow configuration loaded for {tool.name}")

                tool_param: Dict[str, Any] = {
                    "type": "function",
                    "function": {
                        "name": tool.name,
                        "description": enhanced_description,
                        "parameters": resolved_schema,
                    },
                }
                available_tools.append(tool_param)

            print(f"✅ Found {len(available_tools)} available tools")
            if self.flow_enhanced_mode:
                flow_enabled_tools = [tool for tool in self.available_tools
                                    if FLOW_SYSTEM_AVAILABLE and flow_loader.load_flow_configuration(tool)]
                if flow_enabled_tools:
                    print(f"🎯 Flow-enhanced tools: {flow_enabled_tools}")
        except Exception as e:
            print(f"❌ Error listing tools from MCP server: {e}")
            raise RuntimeError(f"Failed to list tools from MCP server: {e}") from e

        # Add flow-enhanced system prompt if available
        enhanced_messages = messages.copy()
        if self.flow_enhanced_mode and FLOW_SYSTEM_AVAILABLE and self.available_tools:
            # Create a comprehensive system prompt that includes flow guidance for all available tools
            system_prompts = []
            for tool_name in self.available_tools:
                flow_config = flow_loader.load_flow_configuration(tool_name)
                if flow_config:
                    enhanced_prompt = flow_loader.get_enhanced_system_prompt(tool_name)
                    system_prompts.append(f"=== {tool_name.upper()} TOOL GUIDANCE ===\n{enhanced_prompt}")

            if system_prompts:
                comprehensive_system_prompt = {
                    "role": "system",
                    "content": f"""You are an intelligent AI assistant with access to MCP tools. You have been enhanced with flow configurations and AUTOMATIC INPUT TRANSFORMATION that enable you to provide an exceptional user experience.

CRITICAL: INPUT TRANSFORMATIONS HAPPEN AUTOMATICALLY
- Phone numbers, dates, times, and other inputs are automatically transformed to the correct format BEFORE tools are called
- You do NOT need to mention formatting, transformation, or ask users to reformat anything
- Simply proceed naturally with the conversation - the system handles all formatting behind the scenes
- NEVER mention "formatting", "transforming", or "adjusting" user input - it happens invisibly

CORE PRINCIPLES:
- Accept natural language input and proceed naturally - transformations are automatic
- Be proactive but conversational in gathering information
- Always confirm understanding before taking actions
- Focus on the user's intent, not technical details
- Proceed seamlessly without mentioning any formatting steps

TOOL-SPECIFIC GUIDANCE:
{chr(10).join(system_prompts)}

Remember: Input transformation is completely automatic and invisible. Your goal is to make interactions as smooth and natural as possible. Users should never hear about technical formatting - just proceed naturally with their requests."""
                }

                # Insert system prompt at the beginning if no system message exists
                if not enhanced_messages or enhanced_messages[0].get("role") != "system":
                    enhanced_messages.insert(0, comprehensive_system_prompt)
                else:
                    # Enhance existing system message
                    enhanced_messages[0]["content"] += f"\n\n{comprehensive_system_prompt['content']}"

                print("🎯 Enhanced system prompt with flow configurations")

        # Initialize OpenAI API call
        try:
            print("🤔 Sending request to OpenAI...")
            response: ChatCompletion = await self.openai.chat.completions.create(
                model="gpt-4o-mini",
                max_tokens=1000,
                messages=enhanced_messages,  # type: ignore
                tools=available_tools if available_tools else None,  # type: ignore
            )
            print("✅ Received response from OpenAI")
        except Exception as e:
            print(f"❌ OpenAI API error: {e}")
            raise RuntimeError(f"Failed to get response from OpenAI: {e}") from e

        final_text = []

        # Process the response content
        if response.choices and response.choices[0].message:
            message = response.choices[0].message
            assistant_content = {"role": "assistant", "content": message.content or ""}

            # Handle text content
            if message.content:
                final_text.append(message.content)

            # Handle tool calls
            if message.tool_calls:
                # Add tool calls to assistant content
                assistant_content["tool_calls"] = []  # type: ignore

                # Build tool calls list for assistant message
                for tool_call in message.tool_calls:
                    if tool_call.function:
                        assistant_content["tool_calls"].append(
                            {
                                "id": tool_call.id,
                                "type": "function",
                                "function": {"name": tool_call.function.name, "arguments": tool_call.function.arguments},
                            }
                        )

                # Add assistant message with tool calls to conversation
                messages.append(assistant_content)
                self.conversation_history.append(assistant_content)

                # Execute tools and process results
                for tool_call in message.tool_calls:
                    if tool_call.function:
                        tool_name = tool_call.function.name
                        # Parse the function arguments
                        try:
                            tool_args: Dict[str, Any] = (
                                json.loads(tool_call.function.arguments) if tool_call.function.arguments else {}
                            )
                        except json.JSONDecodeError as e:
                            print(f"⚠️  Warning: Failed to parse tool arguments for {tool_name}: {e}")
                            tool_args = {}

                        # Apply automatic input transformations if flow configuration is available
                        if self.flow_enhanced_mode and FLOW_SYSTEM_AVAILABLE:
                            tool_args = self._apply_input_transformations(tool_name, tool_args)

                        try:
                            # Execute the tool
                            print(f"🔧 Executing tool: {tool_name}")
                            result = await self.session.call_tool(tool_name, tool_args)
                            final_text.append(
                                f"[Calling tool {tool_name} with arguments {json.dumps(tool_args, indent=2)}]"
                            )

                            # Convert MCP result content to string for OpenAI API
                            result_content = ""
                            if hasattr(result, "content") and result.content:
                                # Handle list of content blocks from MCP
                                if isinstance(result.content, list):
                                    for content_block in result.content:
                                        if isinstance(content_block, TextContent):
                                            result_content += content_block.text
                                        else:
                                            result_content += str(content_block)
                                else:
                                    result_content = str(result.content)
                            else:
                                result_content = str(result)

                            print(f"✅ Tool {tool_name} executed successfully")

                            # Add tool result message to conversation
                            tool_result_message = {
                                "role": "tool",
                                "tool_call_id": tool_call.id,
                                "content": result_content,
                            }
                            messages.append(tool_result_message)
                            self.conversation_history.append(tool_result_message)

                        except Exception as e:
                            print(f"❌ Error executing tool {tool_name}: {e}")
                            # Add error result to conversation so OpenAI can handle it
                            error_result_message = {
                                "role": "tool",
                                "tool_call_id": tool_call.id,
                                "content": f"Error executing tool {tool_name}: {str(e)}",
                            }
                            messages.append(error_result_message)
                            self.conversation_history.append(error_result_message)

                # Get OpenAI's response to the tool results
                try:
                    print("🤔 Getting OpenAI's response to tool results...")

                    # Update enhanced_messages with the latest conversation state
                    if self.flow_enhanced_mode and enhanced_messages[0].get("role") == "system":
                        # Keep the system prompt and update the rest
                        updated_enhanced_messages = [enhanced_messages[0]] + messages[1:]
                    else:
                        updated_enhanced_messages = messages

                    follow_up_response: ChatCompletion = await self.openai.chat.completions.create(
                        model="gpt-4o",
                        max_tokens=1000,
                        messages=updated_enhanced_messages,  # type: ignore
                        tools=available_tools if available_tools else None,  # type: ignore
                    )
                    print("✅ Received follow-up response from OpenAI")
                except Exception as e:
                    print(f"❌ OpenAI follow-up API error: {e}")
                    # Add a fallback response so the conversation doesn't break
                    final_text.append(f"I executed the requested tools but encountered an error getting the follow-up response: {e}")
                    return "\n".join(final_text)

                # Add the follow-up response to final text and conversation history
                if follow_up_response.choices and follow_up_response.choices[0].message:
                    follow_up_message = follow_up_response.choices[0].message
                    if follow_up_message.content:
                        final_text.append(follow_up_message.content)

                        # Add follow-up response to conversation history
                        follow_up_assistant_content = {"role": "assistant", "content": follow_up_message.content}
                        self.conversation_history.append(follow_up_assistant_content)
            else:
                # No tool calls, just add the assistant message
                self.conversation_history.append(assistant_content)

        return "\n".join(final_text)

    async def chat(self):
        """
        Interactive Chat Loop with enhanced error handling.

        Provides a user-friendly interface for chatting with OpenAI while
        handling various error conditions gracefully.
        """
        print("\nMCP Client Started!")
        print("Type your queries or 'quit' to exit.")
        print("Type 'clear' to reset conversation history.")
        print("OpenAI GPT has access to the MCP server tools and can use them to help you.")

        if self.flow_enhanced_mode:
            print("🎯 Flow Enhancement: Enabled - AI will handle natural language input intelligently!")
        else:
            print("⚠️  Flow Enhancement: Disabled - flows directory not found")
        print()

        while True:
            try:
                query = input("> ").strip()
                if query.lower() in ("quit", "exit", "q"):
                    print("Alright then, Goodbye!")
                    break

                if query.lower() == "clear":
                    self.clear_conversation_history()
                    continue

                if not query:
                    continue

                print("🤔 OpenAI is thinking...")
                response = await self.query(query)
                print("\n" + "=" * 50)
                print("🤖 OpenAI's response:")
                print("=" * 50)
                print(response)
                print("=" * 50 + "\n")

            except KeyboardInterrupt:
                print("\n\nChat interrupted. Type 'quit' to exit gracefully.")
                break
            except ConnectionError as e:
                print(f"\n❌ Connection error: {e}")
                print("The MCP server may have disconnected. Please restart the client.")
                break
            except Exception as e:
                print(f"\n❌ Error: {str(e)}")
                print("Please try again or type 'quit' to exit.")


# Example usage
async def main():
    """
    Example usage of the MCPClient with OpenAI over HTTP transport.

    This client connects to an MCP server running over HTTP (such as the one
    started by running 'python main.py' in this repository) and provides
    an interactive chat interface where OpenAI GPT can use the available MCP tools.
    """
    # Default to local server if no URL provided
    if len(sys.argv) == 1:
        server_url = "http://localhost:8000/mcp"
        print(f"No server URL provided, using default: {server_url}")
    elif len(sys.argv) == 2:
        server_url = sys.argv[1]
    else:
        print("Usage: python client_openai.py [server_url]")
        print("Examples:")
        print("  python client_openai.py")
        print("  python client_openai.py http://localhost:8000/mcp")
        print("  python client_openai.py https://your-server.com/mcp")
        sys.exit(1)

    client = MCPClient()

    try:
        # Connect to your MCP server over HTTP
        print(f"Connecting to MCP server at {server_url}...")
        await client.connect(server_url=server_url)

        print("\n" + "=" * 50)
        print("🤖 OpenAI MCP Client with HTTP Transport")
        print("=" * 50)
        print("Connected successfully! You can now chat with OpenAI GPT.")
        print("OpenAI GPT has access to the MCP tools from the connected server.")
        print("Type 'quit' to exit.")
        print("=" * 50)

        await client.chat()

    except KeyboardInterrupt:
        print("\n\nInterrupted by user")
    except Exception as e:
        print(f"Error: {e}")
        print("\nTroubleshooting:")
        print("1. Make sure the MCP server is running")
        print("2. Verify the server URL is correct")
        print("3. Check that the server supports HTTP transport")
        print("4. Ensure your OPENAI_API_KEY environment variable is set")
    finally:
        await client.disconnect()


if __name__ == "__main__":
    asyncio.run(main())
