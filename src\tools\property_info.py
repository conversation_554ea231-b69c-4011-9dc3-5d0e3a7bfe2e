"""
get_knock_property_info tools for the Knock MCP Server.

This module contains FastAPI endpoints for guest card operations that will be
automatically converted into MCP tools by FastApiMCP.
"""

import logging
from typing import Any

from fastapi import APIRouter, HTTPException, Query, status

from src.models.knock_admin_property_info import (
    AdminProperty,
    AdminPropertyData,
    AdminPropertyResponse,
    AffordableHouseProgram,
    AffordableHousing,
    Amenity,
    AmenityTypes,
    BusinessHours,
    Costable,
    CustomFee,
    Description,
    FeaturedAmenities,
    FeaturedAmenityType,
    MessageTemplates,
    PreferencesSettings,
    PropertyPreferences,
    Settings,
    SnippetConfig,
    Utilities,
    UtilityType,
    Video,
    Website,
)
from src.models.knock_non_admin_property_info import (
    Address,
    Application,
    Doorway,
    GeoLocation,
    InvalidPropertyIdError,
    KeySellingPoints,
    LeaseLength,
    Leasing,
    LeasingTerms,
    Location,
    PetPolicy,
    Pets,
    Property,
    PropertyData,
    PropertyResponse,
    QuickLaunchMenuItem,
    Social,
    get_iso_datetime,
)
from src.models.ldp_renter_datahub_property_info import (
    CollectedData,
    DataSourceInfo,
    LDPRenterDatahubPropertyResponse,
    Metadata,
    PropertyRecord,
)

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

router = APIRouter()


def validate_property(property_id: int) -> int:
    if property_id is None:
        raise InvalidPropertyIdError("Property ID is required.")
    if not isinstance(property_id, int) or property_id <= 0:
        raise InvalidPropertyIdError("Property ID must be a positive integer.")
    return property_id


def register_property_info_tools(mcp: Any) -> None:
    @mcp.tool(
        name="get_knock_non_admin_property_info",
        description="Get the property info of non admin knock properties.",
    )
    def knock_non_admin_property_info(property_id: int) -> PropertyResponse:
        return get_knock_non_admin_property_info(property_id)

    @mcp.tool(
        name="get_knock_admin_property_info",
        description="Get the property info of admin knock properties.",
    )
    def knock_admin_property_info(property_id: int) -> AdminPropertyResponse:
        return get_knock_admin_property_info(property_id)

    @mcp.tool(
        name="get_ldp_renter_datahub_property_info",
        description="Get property info from LDP Renter DataHub.",
    )
    async def ldp_renter_datahub_property_info(property_id: int) -> LDPRenterDatahubPropertyResponse:
        response: LDPRenterDatahubPropertyResponse = get_ldp_renter_datahub_property_info(property_id)
        return response


@router.get(
    "/non-admin/{property_id}",
    operation_id="get_knock_non_admin_property_info",
    response_model=PropertyResponse,
    description="Get non-admin property info",
)
async def get_non_admin_property_info_endpoint(property_id: int) -> PropertyResponse:
    return get_knock_non_admin_property_info(property_id)


@router.get(
    "/admin/{property_id}",
    operation_id="get_knock_admin_property_info",
    response_model=AdminPropertyResponse,
    description="Get admin property info",
)
async def get_admin_property_info_endpoint(property_id: int) -> AdminPropertyResponse:
    return get_knock_admin_property_info(property_id)


@router.get(
    "/ldp-renter-datahub/{property_id}",
    operation_id="get_ldp_renter_datahub_property_info",
    response_model=LDPRenterDatahubPropertyResponse,
    description="Get property info from LDP Renter DataHub",
)
def get_ldp_renter_datahub_property_info(property_id: int) -> LDPRenterDatahubPropertyResponse:
    """Get property info from LDP Renter DataHub."""
    if property_id is None:
        raise InvalidPropertyIdError("Property ID is required.")

    try:
        validated_property = validate_property(property_id)
    except InvalidPropertyIdError as e:
        raise HTTPException(status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)) from e

    logger.info(
        "Getting LDP Renter DataHub property info for property %s",
        property_id,
    )

    mock_filtered_data: dict[str, Any] = {
        "active": True,
        "aggressiveBreedsAllowed": False,
        "buildingType": "apartment",
        "currencyName": "Dollar",
        "currencySymbol": "$",
        "drivingDirection": "Take TX-1604 Loop N exit Potranco Rd or FM 1957. Please call for driving directions.",
        "fullDescription": "Mock property description",
        "largeBreedsAllowed": False,
        "leaseLength": "12,13,14,15,16,17,18",
        "maxUnitsShownToProspect": 6,
        "name": f"Property {property_id}",
        "parkingAssignmentAvailable": True,
        "parkingComment": "Covered Parking and Garages Available",
        "parkingType": "Multiple",
        "petCare": False,
        "petPolicyRestrictions": "Two Pet Limit, breed restrictions apply.",
        "petPolicyType": "Cat, Dogs",
        "petRentBasis": "Per Pet",
        "petsAllowed": True,
        "propertyOfficeHours": [
            {"dayOfWeek": "Monday", "endTime1": "18:00", "startTime1": "09:00"},
            {"dayOfWeek": "Tuesday", "endTime1": "18:00", "startTime1": "09:00"},
            {"dayOfWeek": "Wednesday", "endTime1": "18:00", "startTime1": "09:00"},
            {"dayOfWeek": "Thursday", "endTime1": "18:00", "startTime1": "09:00"},
            {"dayOfWeek": "Friday", "endTime1": "18:00", "startTime1": "09:00"},
            {"dayOfWeek": "Saturday", "endTime1": "17:00", "startTime1": "10:00"},
        ],
        "propertyType": "Conventional",
        "shortDescription": "Luxury. Location. Lifestyle.",
        "specials": [
            {"effectiveFromDate": "2023-10-23 00:00 -0500", "specialDisplayText": "Limited Time! 6 Weeks FREE"}
        ],
        "structureType": "Unspecified",
        "symbolOfArea": "Sq.Ft",
        "symbolOfWeight": "lb",
        "unitOfArea": "Square Feet",
        "unitOfWeight": "Pounds",
        "walkScore": 8,
        "walkScoreDescription": "Car-Dependent",
        "walkScoreWSLink": f"https://www.walkscore.com/score/{property_id}",
        "webSiteUrl": f"https://example.com/property/{property_id}",
    }

    mock_collected_data = CollectedData(
        applicant=DataSourceInfo(filtered_data=None, summary=None),
        data_source="LeaseStar",
        prospect=DataSourceInfo(filtered_data=mock_filtered_data, summary=None),
        raw_data={"id": property_id},
        resident=DataSourceInfo(filtered_data=None, summary=None),
    )

    metadata = Metadata(
        next_offset=0,
        query_time_ms=426,
        row_count=1,
    )

    records = [
        PropertyRecord(
            applicant_summary=None,
            collected_data=[mock_collected_data],
            extras={},
            processed_at=None,
            property_id=str(property_id),
            property_source="LDP",
            prospect_summary=None,
        )
    ]

    response = LDPRenterDatahubPropertyResponse(
        metadata=metadata,
        records=records,
    )
    return response


def get_knock_admin_property_info(property_id: int) -> AdminPropertyResponse:
    if property_id is None:
        raise InvalidPropertyIdError("Property ID is required.")

    try:
        validated_property = validate_property(property_id)
    except InvalidPropertyIdError as e:
        raise HTTPException(status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)) from e

    logger.info(
        "Getting admin property info for property %s",
        property_id,
    )

    # Mock implementation
    mock_property = AdminProperty(
        created_time=get_iso_datetime(),
        custom_fees=[
            CustomFee(amount=100.0, name="Admin Fee", property_id=property_id),
            CustomFee(amount=150.0, name="Moveout cleaning fee", property_id=property_id),
            CustomFee(amount=2.0, name="Pest Control Fee", property_id=property_id),
            CustomFee(amount=100.0, name="Cable & Internet", property_id=property_id),
        ],
        data=AdminPropertyData(
            affordableHousing=AffordableHousing(
                notes="We do participate in affordable housing programs.",
                programs=[
                    AffordableHouseProgram(available=False, name="Affordable house 1"),
                    AffordableHouseProgram(available=False, name="Affordable house 2"),
                    AffordableHouseProgram(available=True, name="Section 8 accepted"),
                ],
            ),
            amenities=AmenityTypes(
                additional=[Amenity(available=True, name="Hardwood floor")],
                exterior=[Amenity(available=True, name="Private pool")],
                heatingAndCooling=[Amenity(available=True, name="Central AC")],
                recreation=[Amenity(available=True, name="Fitness center")],
                security=[Amenity(available=True, name="Controlled access")],
                wiring=[Amenity(available=True, name="High-speed internet")],
            ),
            appliances=[Amenity(available=True, name="Dishwasher")],
            costables=[Costable(isCovered=False, name="Covered cost one")],
            created_time=get_iso_datetime(),
            debug={},
            description=Description(short="Experience the exceptional lifestyle."),
            doorway=Doorway(
                agentGuidedTourDisclaimerText="",
                applyNowIsActive=True,
                availabilityIsActive=True,
                bannerText="Welcome to our community!",
                customAppointmentMessage="",
                dynamicNumberInsertionType="",
                excludeAppointmentMessagePhone=False,
                faqsIsActive=True,
                formattedNumber="",
                formattedNumberIsActive=False,
                galleryIsActive=True,
                hideKnockBranding=False,
                hidePricing=False,
                hideUnitPreferences=False,
                hideUnitSelector=False,
                includeInQuickLaunchMenuList=[
                    QuickLaunchMenuItem(enabled=True, name="schedule"),
                    QuickLaunchMenuItem(enabled=True, name="gallery"),
                ],
                leasingInfoIsActive=True,
                leasingSpecialIsActive=True,
                limitAvailableUnitsShown=False,
                liveVideoTourDisclaimerText="",
                neighborhoodIsActive=True,
                petInfoIsActive=True,
                renterPortalIsActive=True,
                requestTextIsActive=True,
                residentPortalURL="https://example.com/portal",
                scheduleATourContentBox="",
                selfGuidedTourDisclaimerText="",
                showNoteInput=False,
                somethingElseIsActive=True,
                useCustomWebsite=False,
                useDynamicFloorplans=True,
            ),
            editorType="manual",
            extra={},
            featured_amenities=FeaturedAmenities(
                gym=FeaturedAmenityType(types=[Amenity(available=True, name="Fitness Center")]),
                laundry=FeaturedAmenityType(types=[Amenity(available=True, name="In Unit")]),
                parking=FeaturedAmenityType(types=[Amenity(available=True, name="Off-Street")]),
                pets=FeaturedAmenityType(types=[Amenity(available=True, name="Small Dogs")]),
                pool=FeaturedAmenityType(types=[Amenity(available=True, name="Shared Pool")]),
            ),
            furnishing=[Amenity(available=True, name="Unfurnished")],
            id=str(property_id),
            key_selling_points=KeySellingPoints(
                community=["Great amenities", "24/7 maintenance"],
                location=["Close to shopping", "Easy highway access"],
                units=["Modern appliances", "Spacious floor plans"],
            ),
            leasing=Leasing(
                application=Application(
                    fee="$50",
                    instructions="Apply online",
                    isDefaultApplicationUrlOverridden=False,
                    link="",
                ),
                provider="Mock Provider",
                qualificationCriteria="Standard qualification criteria applies",
                terms=LeasingTerms(
                    breakPenalty="Two months rent",
                    deposit="$500",
                    includeUpcharges=False,
                    leaseLengths=[LeaseLength(isAvailable=True, leaseLength="12", lengthUnit="months")],
                    leasingSpecial="First month free",
                    notes="",
                ),
            ),
            location=Location(
                address=Address(
                    city="Sample City",
                    house="123 Main St",
                    neighborhood="Downtown",
                    raw="123 Main St, Sample City, CA 12345",
                    state="CA",
                    street="123 Main St",
                    zip="12345",
                ),
                geo=GeoLocation(coordinates=[-122.419416, 37.774929], type="point"),
                name=f"Property {property_id}",
                needs_review=False,
                numberOfUnits=100,
                timezone="America/Los_Angeles",
                yearBuilt=2020,
            ),
            logos=[],
            notes="",
            pets=Pets(
                allowed=PetPolicy(cats=True, large_dogs=False, none=False, small_dogs=True),
                deposit="$300",
                fee="$50",
                notes="Weight limit 25 lbs",
                rent="$30",
            ),
            property_id=property_id,
            social=Social(
                facebook="",
                knock_email="<EMAIL>",
                shortlink="https://example.com/p/123",
                website="https://example.com",
            ),
            updated_time=get_iso_datetime(),
            utilities=Utilities(
                estimatedCost="",
                types=[
                    UtilityType(included=True, name="Water"),
                    UtilityType(included=False, name="Electricity"),
                ],
            ),
            videos=[Video(thumb_url="", url="https://example.com/video")],
            views=[Amenity(available=True, name="City")],
            website=Website(primaryColor="#3FA9F5"),
        ),
        id=property_id,
        is_deleted=False,
        leasing_team_id=1000,
        modified_time=get_iso_datetime(),
        owning_manager_id=2000,
        preferences=PropertyPreferences(
            created_time=get_iso_datetime(),
            disable_pricing_availability=False,
            enable_affordable_housing=False,
            enable_selfie_scan=False,
            id=1601,
            is_deleted=False,
            modified_time=get_iso_datetime(),
            preferences={
                "ai_cross_sell_availability_url": "https://example.com",
                "in_person_tours": True,
                "live_video_tour_type": True,
            },
            property_id=property_id,
            require_lease_term=False,
            require_preferred_bedroom_count=False,
            require_preferred_unit=False,
            require_prospect_floorplan=False,
            require_prospect_move_in_date=False,
            require_unit_scheduling=False,
            suppressed_autoresponders=["winback"],
            suppressed_outbound_channels=[],
        ),
        public_id=f"prop_{property_id}",
        resource_id=3000,
        timezone="America/Los_Angeles",
        type="multi-family",
    )

    mock_settings = Settings(
        hours={
            "1": BusinessHours(end_time="17:00:00", is_active=True, start_time="09:00:00"),
            "2": BusinessHours(end_time="17:00:00", is_active=True, start_time="08:00:00"),
        },
        message_templates=MessageTemplates(
            away="Thanks for visiting! We are away right now.",
            away_response="Thanks for the info!",
            greeting="Thanks for visiting!",
            guest="Thanks for visiting!",
        ),
        preferences=PreferencesSettings(
            button_color="#3FA9F5",
            button_greeting="Questions?",
            display_launcher_button=True,
            emit_analytics_events=False,
            emit_tag_manager_events=False,
            messaging_enabled=True,
            primary_color="#3FA9F5",
            script_major_version="3",
        ),
        snippet="<script>console.log('mock snippet')</script>",
        snippetConfig=SnippetConfig(
            community_id="test_id",
            host="https://example.com",
            token="test_token",
        ),
    )

    return AdminPropertyResponse(
        property=mock_property,
        settings=mock_settings,
        status_code="ok",
    )


def get_knock_non_admin_property_info(property_id: int) -> PropertyResponse:
    if property_id is None:
        raise InvalidPropertyIdError("Property ID is required.")

    try:
        validated_property = validate_property(property_id)
    except InvalidPropertyIdError as e:
        raise HTTPException(status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)) from e

    logger.info(
        "Getting property info for property %s",
        property_id,
    )

    """
    This endpoint fetches the details of a knock property.
    """
    # Mock implementation
    mock_property = Property(
        created_time=get_iso_datetime(),
        data=PropertyData(
            doorway=Doorway(
                agentGuidedTourDisclaimerText="",
                applyNowIsActive=True,
                availabilityIsActive=True,
                bannerText="Welcome to our community!",
                customAppointmentMessage="",
                dynamicNumberInsertionType="",
                excludeAppointmentMessagePhone=False,
                faqsIsActive=True,
                formattedNumber="",
                formattedNumberIsActive=False,
                galleryIsActive=True,
                hideKnockBranding=False,
                hidePricing=False,
                hideUnitPreferences=False,
                hideUnitSelector=False,
                includeInQuickLaunchMenuList=[
                    QuickLaunchMenuItem(enabled=True, name="schedule"),
                    QuickLaunchMenuItem(enabled=True, name="gallery"),
                ],
                leasingInfoIsActive=True,
                leasingSpecialIsActive=True,
                limitAvailableUnitsShown=False,
                liveVideoTourDisclaimerText="",
                neighborhoodIsActive=True,
                petInfoIsActive=True,
                renterPortalIsActive=True,
                requestTextIsActive=True,
                residentPortalURL="https://example.com/portal",
                scheduleATourContentBox="",
                selfGuidedTourDisclaimerText="",
                showNoteInput=False,
                somethingElseIsActive=True,
                useCustomWebsite=False,
                useDynamicFloorplans=True,
            ),
            id=str(property_id),
            key_selling_points=KeySellingPoints(
                community=["Great amenities", "24/7 maintenance"],
                location=["Close to shopping", "Easy highway access"],
                units=["Modern appliances", "Spacious floor plans"],
            ),
            leasing=Leasing(
                application=Application(
                    fee="$50",
                    instructions="Apply online",
                    isDefaultApplicationUrlOverridden=False,
                    link="",
                ),
                provider="Mock Provider",
                qualificationCriteria="Standard qualification criteria applies",
                terms=LeasingTerms(
                    breakPenalty="Two months rent",
                    deposit="$500",
                    includeUpcharges=False,
                    leaseLengths=[LeaseLength(isAvailable=True, leaseLength="12", lengthUnit="months")],
                    leasingSpecial="First month free",
                    notes="",
                ),
            ),
            location=Location(
                address=Address(
                    city="Sample City",
                    house="123 Main St",
                    neighborhood="Downtown",
                    raw="123 Main St, Sample City, CA 12345",
                    state="CA",
                    street="123 Main St",
                    zip="12345",
                ),
                geo=GeoLocation(coordinates=[-122.419416, 37.774929], type="point"),
                name=f"Property {property_id}",
                needs_review=False,
                numberOfUnits=100,
                timezone="America/Los_Angeles",
                yearBuilt=2020,
            ),
            logos=[],
            notes="",
            pets=Pets(
                allowed=PetPolicy(cats=True, large_dogs=False, none=False, small_dogs=True),
                deposit="$300",
                fee="$50",
                notes="Weight limit 25 lbs",
                rent="$30",
            ),
            property_id=property_id,
            social=Social(
                facebook="",
                knock_email="<EMAIL>",
                shortlink="https://example.com/p/123",
                website="https://example.com",
            ),
        ),
        id=property_id,
        is_deleted=False,
        leasing_team_id=1000,
        modified_time=get_iso_datetime(),
        owning_manager_id=2000,
        public_id=f"prop_{property_id}",
        resource_id=3000,
        timezone="America/Los_Angeles",
        type="multi-family",
    )

    return PropertyResponse(property=mock_property, status_code="ok")


async def knock_non_admin_property_info(
    property_id: int = Query(..., description="The ID of the property to get the knock admin property info for."),
) -> PropertyResponse:
    return get_knock_non_admin_property_info(property_id)


async def knock_admin_property_info(
    property_id: int = Query(..., description="The ID of the property to get the knock admin property info for."),
) -> AdminPropertyResponse:
    return get_knock_admin_property_info(property_id)
