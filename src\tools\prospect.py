"""
Guest card update tools for the Knock MCP Server.

This module contains FastAPI endpoints for guest card operations that will be
automatically converted into MCP tools by FastApiMCP.
"""

import json
import time
from datetime import datetime

from fastapi import APIRouter, HTTPException
from fastmcp import FastMCP
from starlette import status

from src.config.consent_config import consent_configs
from src.custom_exceptions.invalid_consent_status_exception import InvalidConsentStatusError
from src.models.prospect import (
    GuestCardPreferences,
    GuestCardProfile,
    GuestCardRequestType,
    Prospect,
    ProspectActivityResponse,
    ProspectStatusResponse,
    SmsConsent,
)
from src.utils.log_config import get_logger

router = APIRouter()
logger = get_logger(__name__)


def update_prospect_guestcard(
    prospect_id: int, profile: GuestCardProfile, preferences: GuestCardPreferences | None = None
) -> GuestCardRequestType:
    """
    Update an existing prospect guestcard.

    This endpoint updates the specified fields of a guestcard.
    Only the provided fields will be updated.

    Only the provided fields will be updated.
    """
    logger.info(f"Starting guestcard update for prospect_id: {prospect_id}")
    logger.debug(f"Request data profile: {profile}")
    logger.debug(f"Request preferences: {preferences}")

    # Simulate fetching existing card and applying updates
    return GuestCardRequestType(profile=profile, preferences=preferences)


def update_prospect_consent_tool(prospect_id: int, consent_info: SmsConsent) -> dict:
    """
    Update the consent status of a prospect.
    """

    try:
        consent_configs.get(consent_info.status.value)
    except InvalidConsentStatusError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e)) from e

    logger.info(f"Consent status: {consent_info.status}")
    logger.info(f"Config: {consent_info}")
    logger.info(f"Updating prospect {prospect_id} consent to {consent_info.status}")

    current_time = datetime.now().replace(microsecond=0).isoformat()

    return {
        "prospect": {
            "id": prospect_id,
            "sms_consent": {
                "bypass_consent": bool(consent_info.bypass_consent),
                "created_time": current_time,
                "has_consent": consent_info.has_consent,
                "has_express_consent_override": bool(consent_info.has_express_consent_override),
                "id": 89611 + prospect_id % 1000,  # Generate unique ID
                "is_deleted": bool(consent_info.is_deleted),
                "modified_time": current_time,
                "note": consent_info.note,
                "status": consent_info.status.value,
            },
            "modified_time": current_time,
        }
    }


def register_prospect_tools(mcp: FastMCP) -> None:
    """
    Register the prospect tools with the MCP server.
    """

    @mcp.tool(
        name="update_prospect_guestcard",
        description="Update an existing prospect guestcard.",
    )
    def update_guestcard(prospect_id: int, request: GuestCardRequestType) -> GuestCardRequestType:
        """
        Update an existing prospect guestcard.

        This endpoint updates the specified fields of a guestcard.
        Only the provided fields will be updated.
        """
        logger.info(f"Starting guestcard update for prospect_id: {prospect_id}")
        logger.debug(f"Request data: {request}")

        # Simulate fetching existing card and applying updates
        return update_prospect_guestcard(prospect_id, request.profile, request.preferences)

    @mcp.tool(
        name="get_prospect_guestcard",
        description="Get the guestcard for a prospect.",
    )
    def get_prospect_guestcard_tool(
        prospect_id: int,
        include_appointments: bool = False,
    ) -> Prospect:
        time.sleep(1)
        return get_prospect_guestcard(prospect_id, include_appointments)

    @mcp.tool(
        name="update_prospect_consent",
        description="Update the consent status of a prospect.",
    )
    def update_prospect_consent_mcp_tool(prospect_id: int, consent_info: SmsConsent) -> dict:
        """
        Update the consent status of a prospect.
        """
        return update_prospect_consent_tool(prospect_id, consent_info)


@router.put("/{prospect_id}/status", operation_id="update_prospect_status", response_model=ProspectStatusResponse)
async def update_prospect_status(prospect_id: int, status: str) -> ProspectStatusResponse:
    """
    Update the status of an existing prospect.

    This endpoint updates the status of the specified prospect.
    """
    logger.info(f"Updating prospect {prospect_id} status to {status}")
    return ProspectStatusResponse(prospect_id=prospect_id, status=status)


@router.post("/{prospect_id}/activity", operation_id="add_prospect_activity", response_model=ProspectActivityResponse)
async def add_prospect_activity(prospect_id: int, activity: str) -> ProspectActivityResponse:
    """
    Add an activity to the prospect's activity log.

    This endpoint adds a new activity to the specified prospect's activity log.
    """
    logger.info(f"Adding activity '{activity}' to prospect {prospect_id}")
    # Mock implementation - in real scenario, this would update database
    return ProspectActivityResponse(prospect_id=prospect_id, activity=activity)


def get_prospect_guestcard(prospect_id: int, include_appointments: bool = False) -> Prospect:
    logger.info(f"Fetching guestcard for prospect {prospect_id}")

    # If prospect is not found, return "NO_PROSPECT_GUEST_CARD_AVAILABLE"

    with open("src/tools/mocked_prospect_response.json") as f:
        response = json.load(f)

    prospect = response.get("prospect")
    if not prospect:
        logger.error(f"No guestcard found for prospect {prospect_id}")
        raise ValueError("NO_PROSPECT_GUEST_CARD_AVAILABLE")

    prospect_sms_inconsistent = 9999
    if prospect_id == prospect_sms_inconsistent:
        prospect["sms_consent"]["status"] = "other"

    prospect["id"] = prospect_id

    # TODO: Parse Prospect Response

    if include_appointments:
        prospect["existing_tour"] = {"id": 1, "start_time": "2025-08-01T10:00:00Z"}

    return Prospect(**prospect)


@router.get(
    "/{prospect_id}",
    operation_id="get_prospect_guestcard",
    response_model=Prospect,
)
async def get_prospect_guestcard_endpoint(
    prospect_id: int,
    include_appointments: bool = False,
) -> Prospect:
    """
    Get the guestcard for a prospect.
    """
    return get_prospect_guestcard(prospect_id, include_appointments)
