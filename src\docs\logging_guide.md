# Knock MCP Server Logging Quick Reference

## How to Use Logging in This Codebase

**1. Import and create a logger in your module:**

```python
from src.utils.log_config import get_logger
logger = get_logger(__name__)
```

**2. Log messages at the appropriate level:**

```python
logger.info("Something happened")
logger.debug(f"Debug details: {details}")
logger.warning("A potential issue occurred")
logger.error("An error occurred")
logger.exception("Exception with stack trace")
```

**3. Example usage in an endpoint:**

```python
from fastapi import APIRouter, HTTPException
from src.utils.log_config import get_logger

router = APIRouter()
logger = get_logger(__name__)

@router.get("/health")
async def health_check():
    logger.info("Health check requested")
    return {"status": "healthy"}
```

**4. Set log level (optional):**

```bash
export LOG_LEVEL=DEBUG
```

**5. Log configuration is in `src/utils/log_config.py`.**

## Best Practices

### 1. Use Appropriate Log Levels

- Use DEBUG for detailed debugging information
- Use INFO for normal operations
- Use WARNING for potential issues
- Use ERROR for actual problems
- Use CRITICAL sparingly

### 2. Include Context

Always include relevant context in your log messages:

```python
# Good
logger.info(f"Processing order {order_id} for customer {customer_id}")

# Bad
logger.info("Processing order")
```

### 3. Avoid Sensitive Information

Never log sensitive data like passwords, API keys, or personal information:

```python
# Good
logger.info(f"User {user_id} authenticated successfully")

# Bad
logger.info(f"User {user_id} authenticated with password: {password}")
```

### 4. Use Exception Logging

When catching exceptions, use `logger.exception()` to automatically include the stack trace:

```python
try:
    # Your code
except Exception as e:
    logger.exception("An error occurred")  # Includes stack trace
    # or
    logger.error(f"An error occurred: {str(e)}")  # Just the message
```

### 5. Performance Considerations

- Use conditional logging for expensive operations
- Avoid string formatting in debug logs if the level is disabled
- Use lazy evaluation for complex data structures

```python
# Good - only formats if debug is enabled
if logger.isEnabledFor(10):
    logger.debug(f"Complex data: {expensive_computation()}")

# Bad - always computes expensive_computation()
logger.debug(f"Complex data: {expensive_computation()}")
```

### 6. Consistent Naming

Use consistent naming patterns for your loggers:

```python
# Use module name
logger = get_logger(__name__)

# Or use a specific name for the tool
logger = get_logger("knock.property_info")
```
