from __future__ import annotations
import asyncio
import json
import sys

from typing import Optional, List
from contextlib import AsyncExitStack
from urllib.parse import urlparse

from mcp import ClientSession
from mcp.client.streamable_http import streamablehttp_client
from mcp.types import TextContent

from openai import AsyncOpenAI
from openai.types.chat import ChatCompletion

from dotenv import load_dotenv
from openai.types.chat.chat_completion_tool_param import ChatCompletionToolParam

load_dotenv()  # Load environment variables from .env file

MODEL = "gpt-4o-mini"
SYSTEM_PROMPT = """**Assistant Role & Behavior**
- You are an intelligent AI assistant with access to a set of tools.
- Automatically perform all input transformations invisibly; never mention "formatting" or "transforming."
- Adhere strictly to each tool's input schema.
- Avoid redundant confirmations (e.g. don't ask "Did you mean tomorrow at 2 PM?" if the user already said it).

**Tool Usage** [VERY IMPORTANT - adhere to this strictly]
	1. If **`profile`** is missing or empty in a `schedule_tour` call, **first** call `get_prospect_guestcard` and get `prospect.profile`.  Default: call `schedule_tour`
    ```json
    	{ "prospect": { "profile": { /* …full profile… */ } } }
    ```
 	2. Only **then** call `schedule_tour` with a non-empty `profile`.
- If the user explicitly asks to reschedule: call `get_schedule_tour_link`
- If a "technical issue" arises: identify and explain the specific issue to the user

**Missing or Incorrect Information**
- Show the data you have so far.
- Prompt the user only for the missing or corrected fields.
- Once all required info is present, call the tool immediately.
- Do **not** apologize, express uncertainty, or claim a generic "error."

**Input Transformations**
- **Dates** -> `"YYYY-MM-DD"`
- **`requested_times`** ->
  ```json
    "requested_times": [
      { "start_time": "2025-08-20T20:30:00-05:00" }
    ]
  ```
- **`phone_number`** ->
  ```json
    "profile": { ..., "phone_number": "(*************" }
  ```
- **`target_move_date`** ->
  ```json
    "profile": { ..., "target_move_date": "2025-08-20" }
  ```
- **Marketing source (default)** ->
  ```json
    "marketing_source": { "code": "direct", "domain": "direct" }
  ```
- **Pets (default)** ->
  ```json
		"profile": { ..., "pets": { "none": true, "large_dogs": false, "small_dogs": false, "cats": false } }
  ```

- Enable specific flags (`cats`, `small_dogs`, `large_dogs`) based on user input.

**Date Logic**

- **"before the tour date"** -> `move_in_date = tour_date - 1 day`
- **"after the tour date"**  -> `move_in_date = tour_date + 1 day`

**On Success**

- Return the tool call with its full response.
- Display the confirmation details to the user.

"""


class GPTClient:
    def __init__(self):
        self.session: Optional[ClientSession] = None
        self.exit_stack = AsyncExitStack()
        self.openai = AsyncOpenAI()
        self.conversation_history = []
        self.server_url = ""

    async def connect(self, server_url: str):
        """Connect to MCP server over HTTP transport

        Args:
            server_url: HTTP URL of the MCP server (e.g., 'http://localhost:8000/mcp')
        """
        try:
            parsed = urlparse(server_url)
            if not parsed.scheme or not parsed.netloc:
                raise ValueError(f"Invalid server URL: {server_url}")
            if parsed.scheme not in ("http", "https"):
                raise ValueError("URL scheme must be http or https")

            self.server_url = server_url
        except Exception as e:
            raise ValueError(f"Invalid server URL '{server_url}': {e}")

        try:
            print("Establishing HTTP transport connection...")
            http_transport = await asyncio.wait_for(
                self.exit_stack.enter_async_context(streamablehttp_client(server_url)), timeout=30.0
            )
            self.read_stream, self.write_stream, self.session_id = http_transport

            print("Creating MCP session...")
            self.session = await self.exit_stack.enter_async_context(ClientSession(self.read_stream, self.write_stream))

            print("Initializing session...")
            await asyncio.wait_for(
                self.session.initialize(), timeout=10.0
            )  # Wait for initialization to complete within 10 seconds

            print("Discovering available tools...")
            # toolsData = await self.session.list_tools()
            # tools = toolsData.tools
            print(f"\n✅ Connected to MCP server at {server_url}")
            # print(f"📋 Available tools: {json.dumps([tool.name for tool in tools], indent=2)}")

            # for tool in tools:
            #     if tool.name == 'schedule_tour':
            #         print(f"Schedule tour tool schema: {json.dumps(tool.inputSchema, indent=2)}")

            # if self.session_id:
            #     print(f"🔗 Session ID: {self.session_id}")

        except Exception as e:
            raise RuntimeError(f"Failed to connect to MCP server at {server_url}: {e}")

    async def disconnect(self):
        try:
            if self.exit_stack:
                print("Disconnecting from MCP server...")
                await self.exit_stack.aclose()
                print("✅ Disconnected successfully")
        except Exception as e:
            print(f"⚠️  Warning during disconnect: {e}")

    def clear_conversation_history(self):
        """Clear the conversation history to start fresh."""
        self.conversation_history.clear()
        print("🔄 Conversation history cleared")

    async def query(self, query: str) -> str:
        """Process query using openai and available tools"""
        self.conversation_history.append(
            {
                "role": "user",
                "content": query,
            }
        )
        messages = [
            {"role": "system", "content": SYSTEM_PROMPT},
            *self.conversation_history,
        ]

        if not self.session:
            raise RuntimeError("Not connected to MCP server. Call connect() first.")

        toolData = await self.session.list_tools()
        tools: List[ChatCompletionToolParam] = [
            {
                "type": "function",
                "function": {
                    "name": tool.name,
                    "description": tool.description or "",
                    "parameters": tool.inputSchema,
                },
            }
            for tool in toolData.tools
        ]
        # print(f"Available tools: {json.dumps(tools, indent=2)}")

        try:
            # print(f"Messages: {json.dumps(messages, indent=2)}")
            response: ChatCompletion = await self.openai.chat.completions.create(
                model=MODEL,
                messages=messages,
                tools=tools,
                parallel_tool_calls=True,
            )
        except Exception as e:
            raise RuntimeError(f"Failed to get response from OpenAI: {e}") from e

        final_text = []

        choice = response.choices[0]

        if choice.finish_reason == "tool_calls":
            tc_msg = choice.message
            messages.append(tc_msg)
            if tc_msg.tool_calls:
                for tool_call in tc_msg.tool_calls:
                    name = tool_call.function.name

                    try:
                        args = json.loads(tool_call.function.arguments) if tool_call.function.arguments else {}
                        # args = json.loads(tool_call.function.arguments)
                    except json.JSONDecodeError as e:
                        print(f"⚠️ Warning: Failed to parse tool arguments for {name}: {e}")
                        args = {}
                    except Exception as e:
                        print(f"⚠️ Warning: Failed to parse tool arguments for {name}: {e}")
                        args = {}

                    if not isinstance(args, dict):
                        print(f"⚠️ Warning: Tool arguments for {name} is not a dictionary: {args}")
                        args = {}

                    result = await self.session.call_tool(name, args)
                    final_text.append(f"[Calling tool {name} with arguments {json.dumps(args, indent=2)}]")

                    # Ensure content is a string
                    if isinstance(result, TextContent):
                        tool_output = result.content
                    elif isinstance(result, (dict, list)):
                        tool_output = json.dumps(result)
                    else:
                        tool_output = str(result)

                    # print(f"Tool output[{name}]: {tool_output}")
                    messages.append(
                        {
                            "role": "tool",
                            "tool_call_id": tool_call.id,
                            "content": tool_output,
                        }
                    )

                final = await self.openai.chat.completions.create(
                    model=MODEL,
                    messages=messages,
                )

                final_text.append(final.choices[0].message.content)

            else:
                final_text.append(choice.message.content)
        else:
            final_text.append(choice.message.content)

        response_str = "\n".join(final_text)
        self.conversation_history.append(
            {
                "role": "assistant",
                "content": response_str,
            }
        )
        print(f"Final Result: {response_str!r}")
        return response_str

    async def chat(self):
        while True:
            try:
                query = input("> ").strip()
                if query.lower() in ("quit", "exit", "q"):
                    print("Alright then, Goodbye!")
                    break

                if query.lower() == "clear":
                    self.conversation_history.clear()
                    continue

                if not query:
                    continue

                print("🤔 GPT is thinking...")
                response = await self.query(query)
                print("\n" + "=" * 50)
                print("🤖 GPT's response:")
                print("=" * 50)
                print(response)
                print("=" * 50 + "\n")

            except KeyboardInterrupt:
                print("\n\nChat interrupted. Type 'quit' to exit gracefully.")
                break
            except ConnectionError as e:
                print(f"\n❌ Connection error: {e}")
                print("The MCP server may have disconnected. Please restart the client.")
                break
            except Exception as e:
                print(f"\n❌ Error: {str(e)}")


async def main():
    if len(sys.argv) == 1:
        server_url = "http://localhost:8000/mcp"
        print(f"No server URL provided, using default: {server_url}")
    elif len(sys.argv) == 2:
        server_url = sys.argv[1]
    else:
        print("Usage: python gptclient.py [server_url]")
        print("Examples:")
        print("  python gptclient.py")
        print("  python gptclient.py http://localhost:8000/mcp")
        print("  python gptclient.py https://your-server.com/mcp")
        sys.exit(1)

    client = GPTClient()
    try:
        print(f"Connecting to MCP server at {server_url}...")
        await client.connect(server_url)
        await client.chat()
    except KeyboardInterrupt:
        print("\n\nInterrupted by user")
    except Exception as e:
        print(f"Error: {e}")
        print("\nTroubleshooting:")
        print("1. Make sure the MCP server is running")
        print("2. Verify the server URL is correct")
        print("3. Check that the server supports HTTP transport")
        print("4. Ensure your ANTHROPIC_API_KEY environment variable is set")
    finally:
        await client.disconnect()


if __name__ == "__main__":
    asyncio.run(main())
