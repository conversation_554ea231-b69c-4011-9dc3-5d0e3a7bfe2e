from src.models.prospect import ConsentStatus

consent_configs = {
    ConsentStatus.GRANTED.value: {"has_consent": True, "status": "granted", "note": None},
    ConsentStatus.DECLINED.value: {"has_consent": False, "status": "declined", "note": "User declined SMS consent"},
    ConsentStatus.REVOKED.value: {"has_consent": False, "status": "revoked", "note": "User revoked SMS consent"},
    ConsentStatus.NEW.value: {"has_consent": False, "status": "new", "note": "New consent request"},
}
